#include "pnglib.h"
// a place to define any shared functions you declare in pnglib.h

int read_header(FILE *fl, pngchunk *chunk) {
    if (fread(&chunk->length, 4, 1, fl) != 1) {
        printf("error reading length\n");
        return -1;
    }
    chunk->length = ntohl(chunk->length);
    if (fread(&chunk->chunk_type, 4, 1, fl) != 1) {
        printf("error reading chunk_type\n");
        return -1;
    }
    chunk->chunk_type[4] = '\0';
    return 0;
}

/* Table of CRCs of all 8-bit messages. */
unsigned long crc_table[256];

/* Flag: has the table been computed? Initially false. */
int crc_table_computed = 0;

/* Make the table for a fast CRC. */
void make_crc_table(void)
{
  unsigned long c;
  int n, k;

  for (n = 0; n < 256; n++) {
    c = (unsigned long) n;
    for (k = 0; k < 8; k++) {
      if (c & 1)
        c = 0xedb88320L ^ (c >> 1);
      else
        c = c >> 1;
    }
    crc_table[n] = c;
  }
  crc_table_computed = 1;
}

/* Update a running CRC with the bytes buf[0..len-1]--the CRC
   should be initialized to all 1's, and the transmitted value
   is the 1's complement of the final running CRC (see the
   crc() routine below). */

unsigned long update_crc(unsigned long crc, unsigned char *buf,
                         int len)
{
  unsigned long c = crc;
  int n;

  if (!crc_table_computed)
    make_crc_table();
  for (n = 0; n < len; n++) {
    c = crc_table[(c ^ buf[n]) & 0xff] ^ (c >> 8);
  }
  return c;
}

/* Return the CRC of the bytes buf[0..len-1]. */
unsigned long crc(unsigned char *buf, int len)
{
  return update_crc(0xffffffffL, buf, len) ^ 0xffffffffL;
}

int write_chunk_to_png(FILE *fl, const char * chunk_type, const char * data_buffer, unsigned int length) {
    length = htonl(length);
    if (fwrite(&length, 4, 1, fl) != 1) {
        return -1;
    }
    if (fwrite(chunk_type, 4, 1, fl) != 1) {
        return 0;
    }
    if (length > 0) {
        printf("data exists\n");
        if (fwrite(data_buffer, length, 1, fl) != 1) {
            return -1;
        }
    }

    uint32_t crc = 0xffffffffL;
    crc = update_crc(crc, (unsigned char *)data_buffer, length);
    crc = htonl(crc);
    if (fwrite(&crc, 4, 1, fl) != 1) {
        return 0;
    }
    return 0;
}


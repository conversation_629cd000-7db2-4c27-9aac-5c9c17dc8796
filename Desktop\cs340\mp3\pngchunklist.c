#include <stdio.h>
#include <arpa/inet.h>
#include "string.h"
#include "pnglib.h"

int main(int argc, char *argv[]) {
    if (argc != 2) {
        fprintf(stderr, "USAGE: %s existingfilename.png\n", argv[0]);
        return 1;
    }
    // argv[1] is the filename of the input .png file
    FILE * fl = fopen(argv[1], "rb");
    if (!fl) {
        printf("error gathering file pointer\n");
        return 0;
    }
    // Skip first 8 header bytes
    fseek(fl, 8, SEEK_SET);

    while (1) {
        unsigned int length;
        char chunk_type[5];
        if (fread(&length, 4, 1, fl) != 1) {
            break; // invalid size read
        }
        length = ntohl(length);

        if (fread(chunk_type, 4, 1, fl) != 1) {
            break; //  invalid type read
        }
        chunk_type[4] = '\0';

        printf("%s %u\n", chunk_type, length);

        if (strcmp(chunk_type, "IEND") == 0) {
            break; // Last chunk
        }

        // Seek past the chunk data and CRC
        fseek(fl, length + 4, SEEK_CUR);
    }
    
    fclose(fl);
    return 0;
}

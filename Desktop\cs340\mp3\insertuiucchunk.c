#include <stdio.h>
#include <stdint.h>
#include "pnglib.h"

// const unsigned char PNG_SIGNATURE[8] = {'89', '50', '4E', '47', '0D', '0A', '1A', '0A'}; // header signature

int main(int argc, char *argv[]) {
    if (argc != 4) {
        fprintf(stderr, "USAGE: %s existingfilename.png filetoinsert result.png\n", argv[0]);
        return 1;
    }
    // argv[1] is the filename of the input .png file
    // argv[2] is the filename of the input data file to copy into the uiuc chunk
    // argv[3] is the filename of the output .png file
    FILE *input_fl = fopen(argv[1], "rb");
    FILE *output_fl = fopen(argv[3], "wb");
    FILE *data_file = fopen(argv[2], "rb");

    long file_size = 0;
    fseek(data_file, 0, SEEK_END);
    file_size = ftell(data_file);
    fseek(data_file, 0, SEEK_SET);
    char *data_buffer = malloc(file_size);
    fread(data_buffer, file_size, 1, data_file); 
    fclose(data_file); 
    printf("data read\n");
    printf("file size is %ld\n", file_size);
    
    // header buffer copy
    unsigned char header[8];
    if (fread(header, 8, 1, input_fl) != 1) {
        fprintf(stderr, "Error: Input file is not a valid PNG file.\n");
        fclose(input_fl);
        fclose(output_fl);
        return -1;
    }
    fwrite(header, 8, 1, output_fl);

    //Chunk processing
    pngchunk chunk;
    int found_replace = 0; // bool for debugging
    int processed_chunks = 0;
    while (read_header(input_fl, &chunk) == 0) {
        if (strcmp(chunk.chunk_type, "IHDR") == 0) {
            printf("read first chunk");
        }
        if (strcmp(chunk.chunk_type, "uiuc") == 0) {
            printf("found 'uiuc' chunk, so replace\n");
            write_chunk_to_png(output_fl, chunk.chunk_type, data_buffer, (unsigned int)file_size);
            fseek(input_fl, chunk.length + 4, SEEK_CUR);
            free(data_buffer);
            found_replace = 1;
        } else if (strcmp(chunk.chunk_type, "IEND") == 0) {
            if (found_replace != 1) {
                printf("No 'uiuc' chunk found. Adding new one before IEND.\n");
                printf(data_buffer);
                write_chunk_to_png(output_fl, "uiuc", data_buffer, (unsigned int)file_size);
                free(data_buffer);
            }
            // Write out IEND chunk itself.
            write_chunk_to_png(output_fl, "IEND", NULL, 0);
            break;
        } else {
            char * buffer_data = malloc(chunk.length);
            fread(buffer_data, chunk.length, 1, input_fl);
            write_chunk_to_png(output_fl, chunk.chunk_type, buffer_data, chunk.length);
            fseek(input_fl, chunk.length + 4, SEEK_CUR);
            free(buffer_data);
        }
        processed_chunks++;
    }
    fclose(input_fl);
    fclose(output_fl);
    return 0;
}

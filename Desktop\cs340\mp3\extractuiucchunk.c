#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "pnglib.h"

int main(int argc, char *argv[]) {
    if (argc != 3) {
        fprintf(stderr, "USAGE: %s existingfilename.png filetosaveto\n", argv[0]);
        return 1;
    }
    // argv[1] is the filename of the input .png file
    // argv[2] is the filename of the output .png file
    FILE * input_fl = fopen(argv[1], "rb");
    // Skip first 8 header bytes
    fseek(input_fl, 8, SEEK_SET);

    pngchunk chunk;
    int found_uiuc_type = 0; // acting like bool
    while (read_header(input_fl, &chunk) == 0) {
        if (strcmp(chunk.chunk_type, "uiuc") == 0) {
            found_uiuc_type = 1;
            char* uiuc_buffer = malloc(chunk.length);
            if (fread(uiuc_buffer, chunk.length, 1, input_fl) != 1) {
                printf("Error reading uiuc chunk\n");
                free(uiuc_buffer);
                fclose(input_fl);
                return -1; 
            }
            FILE * output_fl = fopen(argv[2], "wb");
            fwrite(uiuc_buffer, chunk.length, 1, output_fl);
            printf("Successfully extracted 'uiuc' chunk to %s\n", argv[2]);
            fclose(output_fl);
            free(uiuc_buffer);
            break;
        }
        if (strcmp(chunk.chunk_type, "IEND") == 0) {
            break; // End of File
        }
        // Seek past the chunk data and CRC
        fseek(input_fl, chunk.length + 4, SEEK_CUR);
    }
    fclose(input_fl);
    if (found_uiuc_type == 0) {
        fprintf(stderr, "Error: 'uiuc' chunk not found\n");
    }
    return 0;
}

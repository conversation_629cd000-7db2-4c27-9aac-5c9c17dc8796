==8506== Invalid read of size 1
==8506==    at 0x48534A0: mempcpy (vg_replace_strmem.c:1700)
==8506==    by 0x48EE6B6: _IO_new_file_xsputn (fileops.c:1237)
==8506==    by 0x48EE6B6: _IO_file_xsputn@@GLIBC_2.2.5 (fileops.c:1198)
==8506==    by 0x48E237B: fwrite (iofwrite.c:39)
==8506==    by 0x4017CB: write_chunk_to_png (pnglib.c:78)
==8506==    by 0x401500: main (insertuiucchunk.c:59)
==8506==  Address 0x4a607ad is 0 bytes after a block of size 13 alloc'd
==8506==    at 0x4844818: malloc (vg_replace_malloc.c:446)
==8506==    by 0x4014C4: main (insertuiucchunk.c:57)
==8506== 
==8506== Invalid read of size 1
==8506==    at 0x48534AE: mempcpy (vg_replace_strmem.c:1700)
==8506==    by 0x48EE6B6: _IO_new_file_xsputn (fileops.c:1237)
==8506==    by 0x48EE6B6: _IO_file_xsputn@@GLIBC_2.2.5 (fileops.c:1198)
==8506==    by 0x48E237B: fwrite (iofwrite.c:39)
==8506==    by 0x4017CB: write_chunk_to_png (pnglib.c:78)
==8506==    by 0x401500: main (insertuiucchunk.c:59)
==8506==  Address 0x4a607ae is 1 bytes after a block of size 13 alloc'd
==8506==    at 0x4844818: malloc (vg_replace_malloc.c:446)
==8506==    by 0x4014C4: main (insertuiucchunk.c:57)
==8506== 
==8506== Syscall param write(buf) points to unaddressable byte(s)
==8506==    at 0x48F2687: __internal_syscall_cancel (cancellation.c:64)
==8506==    by 0x48F26AC: __syscall_cancel (cancellation.c:75)
==8506==    by 0x4967935: write (write.c:26)
==8506==    by 0x48EE5F4: _IO_file_write@@GLIBC_2.2.5 (fileops.c:1182)
==8506==    by 0x48EC8D1: new_do_write (fileops.c:450)
==8506==    by 0x48EE7F8: _IO_new_file_xsputn (fileops.c:1256)
==8506==    by 0x48EE7F8: _IO_file_xsputn@@GLIBC_2.2.5 (fileops.c:1198)
==8506==    by 0x48E237B: fwrite (iofwrite.c:39)
==8506==    by 0x4017CB: write_chunk_to_png (pnglib.c:78)
==8506==    by 0x401500: main (insertuiucchunk.c:59)
==8506==  Address 0x4a61790 is 4,000 bytes inside an unallocated block of size 4,175,856 in arena "client"
==8506== 
==8506== Syscall param write(buf) points to uninitialised byte(s)
==8506==    at 0x48F2687: __internal_syscall_cancel (cancellation.c:64)
==8506==    by 0x48F26AC: __syscall_cancel (cancellation.c:75)
==8506==    by 0x4967935: write (write.c:26)
==8506==    by 0x48EE5F4: _IO_file_write@@GLIBC_2.2.5 (fileops.c:1182)
==8506==    by 0x48EC8D1: new_do_write (fileops.c:450)
==8506==    by 0x48EE7F8: _IO_new_file_xsputn (fileops.c:1256)
==8506==    by 0x48EE7F8: _IO_file_xsputn@@GLIBC_2.2.5 (fileops.c:1198)
==8506==    by 0x48E237B: fwrite (iofwrite.c:39)
==8506==    by 0x4017CB: write_chunk_to_png (pnglib.c:78)
==8506==    by 0x401500: main (insertuiucchunk.c:59)
==8506==  Address 0x4abb1a8 is 56 bytes inside a block of size 3,145 alloc'd
==8506==    at 0x4844818: malloc (vg_replace_malloc.c:446)
==8506==    by 0x4014C4: main (insertuiucchunk.c:57)
==8506== 

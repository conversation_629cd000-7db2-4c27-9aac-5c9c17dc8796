// a place to declare any shared functions you want to use in more than one program
#include <stdio.h>
#include <stdlib.h>
#include <arpa/inet.h> 
#include <string.h>    

typedef struct 
{
    unsigned int length;
    char chunk_type[5];
} pngchunk;

int read_header(FILE *fl, pngchunk *chunk);

int write_chunk_to_png(FILE *fl, const char * chunk_type, const char * data_buffer, unsigned int length);



{"configurations": [{"name": "Debug pngchunklist", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/pngchunklist", "args": ["${input:srcpng}"], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "/usr/bin/gdb", "setupCommands": [{"description": "Enable pretty-printing in GDB", "text": "--enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "Makefile build"}, {"name": "Debug extractuiucchunk", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/extractuiucchunk", "args": ["${input:srcpng}", "${input:dstfile}"], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "/usr/bin/gdb", "setupCommands": [{"description": "Enable pretty-printing in GDB", "text": "--enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "Makefile build"}, {"name": "Debug <PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/insertuiucchunk", "args": ["${input:srcpng}", "${input:srcfile}", "${input:dstfile}"], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "/usr/bin/gdb", "setupCommands": [{"description": "Enable pretty-printing in GDB", "text": "--enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "Makefile build"}, {"type": "cppdbg", "name": "Test with <PERSON><PERSON><PERSON>", "request": "launch", "program": "/usr/bin/make", "args": ["test"], "cwd": "${workspaceFolder}", "preLaunchTask": "Makefile build"}], "inputs": [{"id": "srcpng", "type": "promptString", "description": "PNG file to open for reading", "default": "img/onered.png"}, {"id": "dstfile", "type": "promptString", "description": "Name of new file to create", "default": "tempfile"}, {"id": "srcfile", "type": "promptString", "description": "Existing file to hide inside created PNG", "default": "<PERSON><PERSON><PERSON>"}], "version": "0.2.0"}